---
license: apache-2.0
language:
- en
base_model:
- Ultralytics/YOLO11
tags:
- yolo
- yolo11
- nsfw
pipeline_tag: object-detection
---

<p align="center">
  <img src="https://cdn-uploads.huggingface.co/production/uploads/63d8d8879dfcfa941d4d7cd9/eDc2NnKqm25E92hesEUQi.png" alt="Logo">
</p>


<h1 style="color:red" style="text-align:center;"> 
  🔞 WARNING: SENSITIVE CONTENT 🔞 
</h1>

<h2>
  THIS MEDIA CONTAINS SENSITIVE CONTENT (I.E. NUDITY, VIOLENCE, PROFANITY, PORN) THAT SOME PEOPLE MAY FIND OFFENSIVE. YOU MUST BE 18 OR OLDER TO VIEW THIS CONTENT.
</h2>

----------
# EraX-Anti-NSFW-V1.1
A Highly Efficient Model for NSFW Detection. Very effective for **pre-publication image and video control**, or for **limiting children's access to harmful publications**.
You can either just predict the classes and their boundingboxes or even mask the predicted harmful object(s) or mask the entire image. 
Please see the deployment codes below.

- **Developed by**:
  - Lê Chí Tài (<EMAIL>)
  - Phạm Đình Thục (<EMAIL>)
  - Mr. Nguyễn Anh Nguyên (<EMAIL>)
- **Model version**: v1.1
- **License**: Apache 2.0

<!-- ## Examples
![Example 01](./examples/img_1.jpg) -->

## Model Details / Overview

- **Model Architecture**: YOLO11 (Nano, Small, Medium)
- **Task**: Object Detection (NSFW Detection)
- **Dataset**: Private datasets (From Internet).
- **Training set**: 40192 images.
- **Validation set**: 3495 images.
- **Classes**: anus, make_love, nipple, penis, vagina.

### Labels
![Labels](./train_result/erax-anti-nsfw-yolo11n-v1.1/labels.jpg)

## Training Configuration
- **Model Weights Files**:
  - Nano: [`erax-anti-nsfw-yolo11n-v1.1.pt`](./erax-anti-nsfw-yolo11n-v1.1.pt) (5.45 MB)
  - Small: [`erax-anti-nsfw-yolo11s-v1.1.pt`](./erax-anti-nsfw-yolo11s-v1.1.pt) (40.5 MB)
  - Medium: [`erax-anti-nsfw-yolo11m-v1.1.pt`](./erax-anti-nsfw-yolo11m-v1.1.pt) (19.2 MB)

- **Training Config**:
  - **Number of Epochs**: 100
  - **Learning Rate**: 0.01
  - **Batch Size**: 336/192/92 (Nano/Small/Medium)
  - **Image Size**: 640x640
  - **Training server**: 4 x NVIDIA RTX A4000 (16GB GDDR6)
  

## Evaluation Metrics
Below are the key metrics from the model evaluation on the validation set:
comming soon
<!-- 
- **Precision**: 0.726
- **Recall**: 0.68
- **mAP50**: 0.724
- **mAP50-95**: 0.434 -->

<!-- | Format                 | Status |  Size (MB) |  metrics/mAP50-95(B)  |  Inference time (ms/im) |   FPS   |
|:-----------------------|:-------|:-----------|:----------------------|:------------------------|:--------|
|  PyTorch               |   ✅   |    38.6    |         0.4332        |          16.97          |  58.91  |
|TorchScript             |	 ✅	  |    77	   |         0.4153	       |          12.09      	 |  82.69  |
|   ONNX                 |   ✅	  |    76.7	   |         0.4153	       |          103.94      	 |  9.62   |
| OpenVINO               |	 ❌	  |    -	   |         -  	       |          -         	 |  -      |
| TensorRT               | 	 ✅	  |    89.6	   |         0.4155	       |          7          	 |  142.92 |
| CoreML                 |	 ❌	  |    -	   |         -  	       |          -         	 |  -      |
| TensorFlow SavedModel  | 	 ✅	  |    192.3   |         0.4153	       |          40.19        	 |  24.88  |
| TensorFlow GraphDef    |	 ✅	  |    76.8	   |         0.4153	       |          36.71        	 |  27.24  |
| TensorFlow Lite        |	 ❌	  |    -	   |         -  	       |          -         	 |  -      |
| TensorFlow Edge TPU    |	 ❌	  |    -	   |         -  	       |          -         	 |  -      |
| TensorFlow.js          |	 ❌	  |    -	   |         -  	       |          -         	 |  -      |
| PaddlePaddle           | 	 ✅	  |    153.3   |         0.4153	       |          1024.24      	 |  0.98   |
| NCNN                   |	 ✅   |    76.6	   |         0.4153	       |          187.36       	 |  5.34   | -->

## Benchmark
  - **CPU: 11th Gen Intel Core(TM) i7-11800H 2.30GHz**
  - **GPU: NVIDIA GeForce RTX 3050 Ti 3902MiB**

<table>
    <thead>
        <tr>
          <th rowspan="2">Format</th>
          <th rowspan="2" width="200">Model</th>
          <th rowspan="2" width="200">Metrics/mAP50-95(B)</th>
          <th colspan="2">GPU</th>
          <th colspan="2">CPU</th>
        </tr>
        <tr>
          <th width="120">Inference time (ms/im)</th>
          <th width="80">FPS</th>
          <th width="120">Inference time (ms/im)</th>
          <th width="80">FPS</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <th rowspan=3>PyTorch</th>
            <th>erax-anti-nsfw-yolo11n-v1.1.pt</th>
            <!-- <th>5.20</th> -->
            <th>0.438</th>
            <th>3.500</th>
            <th style="color:red">286</th>
            <th>27.900</th>
            <th>36</th>
        </tr>
        <tr>
            <th>erax-anti-nsfw-yolo11s-v1.1.pt</th>
            <!-- <th>18.30</th> -->
            <th>0.453</th>
            <th>7.000</th>
            <th>143</th>
            <th>71.000</th>
            <th>14</th>
        </tr>
        <tr>
            <th>erax-anti-nsfw-yolo11m-v1.1.pt</th>
            <!-- <th>38.60</th> -->
            <th>0.467</th>
            <th>16.500</th>
            <th>61</th>
            <th>206.600</th>
            <th>5</th>
        </tr>
        <tr>
            <th rowspan=3>TorchScript</th>
            <th>erax-anti-nsfw-yolo11n-v1.1.torchscript</th>
            <!-- <th>10.40</th> -->
            <th>0.435</th>
            <th>3.700</th>
            <th>270</th>
            <th>38.500</th>
            <th>26</th>
        </tr>
        <tr>
            <th>erax-anti-nsfw-yolo11s-v1.1.torchscript</th>
            <!-- <th>36.40</th> -->
            <th>0.449</th>
            <th>8.100</th>
            <th>123</th>
            <th>108.500</th>
            <th>9</th>
        </tr>
        <tr>
            <th>erax-anti-nsfw-yolo11m-v1.1.torchscript</th>
            <!-- <th>77.00</th> -->
            <th>0.463</th>
            <th>20.300</th>
            <th>49</th>
            <th>394.900</th>
            <th>3</th>
        </tr>
        <tr>
            <th rowspan=3>ONNX</th>
            <th>erax-anti-nsfw-yolo11n-v1.1.onnx</th>
            <!-- <th>10.10</th> -->
            <th>0.435</th>
            <th>-</th>
            <th>-</th>
            <th>28.300</th>
            <th>35</th>
        </tr>
        <tr>
            <th>erax-anti-nsfw-yolo11s-v1.1.onnx</th>
            <!-- <th>36.20</th> -->
            <th>0.449</th>
            <th>-</th>
            <th>-</th>
            <th>59.800</th>
            <th>17</th>
        </tr>
        <tr>
            <th>erax-anti-nsfw-yolo11m-v1.1.onnx</th>
            <!-- <th>76.70</th> -->
            <th>0.463</th>
            <th>-</th>
            <th>-</th>
            <th>157.800</th>
            <th>6</th>
        </tr>
        <tr>
            <th rowspan=3>OpenVINO</th>
            <th>erax-anti-nsfw-yolo11n-v1.1_openvino_model</th>
            <!-- <th>5.20</th> -->
            <th>0.435</th>
            <th>13.900</th>
            <th>72</th>
            <th>15.900</th>
            <th style="color:red">63</th>
        </tr>
        <tr>
            <th>erax-anti-nsfw-yolo11s-v1.1_openvino_model</th>
            <!-- <th>18.30</th> -->
            <th>0.449</th>
            <th>72.300</th>
            <th>14</th>
            <th>40.800</th>
            <th>25</th>
        </tr>
        <tr>
            <th>erax-anti-nsfw-yolo11m-v1.1_openvino_model</th>
            <!-- <th>38.60</th> -->
            <th>0.463</th>
            <th>245.900</th>
            <th>4</th>
            <th>121.700</th>
            <th>8</th>
        </tr>
        <tr>
            <th rowspan=3>TensorRT</th>
            <th>erax-anti-nsfw-yolo11n-v1.1.engine</th>
            <!-- <th>15.60</th> -->
            <th>0.435</th>
            <th>3.500</th>
            <th style="color:red">286</th>
            <th>-</th>
            <th>-</th>
        </tr>
        <tr>
            <th>erax-anti-nsfw-yolo11s-v1.1.engine</th>
            <!-- <th>47.30</th> -->
            <th>0.449</th>
            <th>6.800</th>
            <th>147</th>
            <th>-</th>
            <th>-</th>
        </tr>
        <tr>
            <th>erax-anti-nsfw-yolo11m-v1.1.engine</th>
            <!-- <th>92.70</th> -->
            <th>0.463</th>
            <th>15.700</th>
            <th>64</th>
            <th>-</th>
            <th>-</th>
        </tr>
        <tr>
            <th rowspan=3>PaddlePaddle</th>
            <th>erax-anti-nsfw-yolo11n-v1.1_paddle_model</th>
            <!-- <th>20.10</th> -->
            <th>0.435</th>
            <th>214.700</th>
            <th>5</th>
            <th>136.200</th>
            <th>7</th>
        </tr>
        <tr>
            <th>erax-anti-nsfw-yolo11s-v1.1_paddle_model</th>
            <!-- <th>72.30</th> -->
            <th>0.449</th>
            <th>517.700</th>
            <th>2</th>
            <th>234.600</th>
            <th>4</th>
        </tr>
        <tr>
            <th>erax-anti-nsfw-yolo11m-v1.1_paddle_model</th>
            <!-- <th>153.30</th> -->
            <th>0.463</th>
            <th>887.000</th>
            <th>1</th>
            <th>506.300</th>
            <th>2</th>
        </tr>
        <tr>
            <th rowspan=3>MNN</th>
            <th>erax-anti-nsfw-yolo11n-v1.1.mnn</th>
            <!-- <th>10.00</th> -->
            <th>0.435</th>
            <th>55.800</th>
            <th>18</th>
            <th>59.300</th>
            <th>17</th>
        </tr>
        <tr>
            <th>erax-anti-nsfw-yolo11s-v1.1.mnn</th>
            <!-- <th>36.10</th> -->
            <th>0.449</th>
            <th>147.600</th>
            <th>7</th>
            <th>146.300</th>
            <th>7</th>
        </tr>
        <tr>
            <th>erax-anti-nsfw-yolo11m-v1.1.mnn</th>
            <!-- <th>76.60</th> -->
            <th>0.463</th>
            <th>378.500</th>
            <th>3</th>
            <th>380.700</th>
            <th>3</th>
        </tr>
        <tr>
            <th rowspan=3>NCNN</th>
            <th>erax-anti-nsfw-yolo11n-v1.1_ncnn_model</th>
            <!-- <th>10.00</th> -->
            <th>0.435</th>
            <th>57.100</th>
            <th>18</th>
            <th>61.100</th>
            <th>16</th>
        </tr>
        <tr>
            <th>erax-anti-nsfw-yolo11s-v1.1_ncnn_model</th>
            <!-- <th>36.10</th> -->
            <th>0.449</th>
            <th>141.200</th>
            <th>7</th>
            <th>137.200</th>
            <th>7</th>
        </tr>
        <tr>
            <th>erax-anti-nsfw-yolo11m-v1.1_ncnn_model</th>
            <!-- <th>76.60</th> -->
            <th>0.463</th>
            <th>375.500</th>
            <th>3</th>
            <th>367.400</th>
            <th>3</th>
        </tr>
    </tbody>
</table>

## Training Validation Results
### Training and Validation Losses
![Training and Validation Losses](./train_result/erax-anti-nsfw-yolo11n-v1.1/results.png)

### Confusion Matrix
![Confusion Matrix](./train_result/erax-anti-nsfw-yolo11n-v1.1/confusion_matrix_normalized.png)


## Inference

To use the trained model, follow these steps:

1. **Install the necessary packages**:
```curl
pip install ultralytics supervision huggingface-hub
```

2. **Download Pretrained model**:
```python
from huggingface_hub import snapshot_download
snapshot_download(repo_id="erax-ai/EraX-Anti-NSFW-V1.1", local_dir="./", force_download=True)
``` 

3. **Simple Use Case**:
```python
from ultralytics import YOLO
from PIL import Image
import supervision as sv
import numpy as np

IOU_THRESHOLD        = 0.3
CONFIDENCE_THRESHOLD = 0.2

# pretrained_path = "erax-anti-nsfw-yolo11m-v1.1.pt"
# pretrained_path = "erax-anti-nsfw-yolo11s-v1.1.pt"
pretrained_path = "erax-anti-nsfw-yolo11n-v1.1.pt"

image_path_list = ["test_images/img_1.jpg", "test_images/img_2.jpg"]

model = YOLO(pretrained_path)
results = model(image_path_list,
                  conf=CONFIDENCE_THRESHOLD,
                  iou=IOU_THRESHOLD
                )


for result in results:
    annotated_image = result.orig_img.copy()
    h, w = annotated_image.shape[:2]
    anchor = h if h > w else w

    detections = sv.Detections.from_ultralytics(result)
    label_annotator = sv.LabelAnnotator(text_color=sv.Color.BLACK,
                                        text_position=sv.Position.CENTER,
                                        text_scale=anchor/1700)
    
    pixelate_annotator = sv.PixelateAnnotator(pixel_size=anchor/50)
    
    annotated_image = pixelate_annotator.annotate(
        scene=annotated_image.copy(),
        detections=detections
    )


    annotated_image = label_annotator.annotate(
        annotated_image,
        detections=detections
    )

    
    sv.plot_image(annotated_image, size=(10, 10))
```

<!-- ## Training
Scripts for training: https://github.com/EraX-JS-Company/EraX-NSFW-V1.0 -->

## More examples

1. **Example 01**:
![Example 03](./examples/img_3.jpg)


2. **Example 02**:
![Example 06](./examples/img_6.jpg)


3. **Example 03**: SAFEEST for using make_love class as it will cover entire context.
Without make_love class    |  With make_love class
:-------------------------:|:-------------------------:
![](./examples/img_2.jpg)  |  ![](./examples/img_2_make_love.jpg)
![](./examples/img_4.jpg)  |  ![](./examples/img_4_make_love.jpg)
![](./examples/img_5.jpg)  |  ![](./examples/img_5_make_love.jpg)



## Citation 
If you find our project useful, we would appreciate it if you could star our repository and cite our work as follows:
```bibtex
@article{EraX-Anti-NSFW-V1.1,
  author    = {Lê Chí Tài and
              Phạm Đình Thục and
              Mr. Nguyễn Anh Nguyên and
              Đoàn Thành Khang and
              Mr. Trần Hải Khương and
              Mr. Trương Công Đức and 
              Phan Nguyễn Tuấn Kha and 
              Phạm Huỳnh Nhật},
  title     = {EraX-Anti-NSFW-V1.1: A Highly Efficient Model for NSFW Detection},
  organization={EraX JS Company},
  year={2024},
  url={https://huggingface.co/erax-ai/EraX-Anti-NSFW-V1.1}
}
```