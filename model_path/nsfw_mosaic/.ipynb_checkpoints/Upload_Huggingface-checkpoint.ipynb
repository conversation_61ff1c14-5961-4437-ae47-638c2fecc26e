#%%
from huggingface_hub import HfApi
api = HfApi()
api.upload_file(
    path_or_fileobj="/home/<USER>/lechitai/Anti-NSFW/EraX-Anti-NSFW-V1.1/README.md",
    path_in_repo="README.md",
    repo_id="erax-ai/EraX-NSFW-V1.1",
    repo_type="model",
    token="*************************************",
)
#%%
from huggingface_hub import HfApi
api = HfApi()

api.upload_folder(
    folder_path="/home/<USER>/lechitai/Anti-NSFW/EraX-Anti-NSFW-V1.1",
    repo_id="erax-ai/EraX-NSFW-V1.1",
    repo_type="model",
    token="*************************************",
)
#%%
pwd
#%%
