from huggingface_hub import snapshot_download
snapshot_download(repo_id="Falconsai/nsfw_image_detection", local_dir="./nsfw_detection", force_download=True, proxies={"https": "http://localhost:7897"})

snapshot_download(repo_id="erax-ai/EraX-Anti-NSFW-V1.1", local_dir="./nsfw_mosaic", force_download=True, proxies={"https": "http://localhost:7897"})

snapshot_download(repo_id="giacomoarienti/nsfw-classifier", local_dir="./nsfw_classifier", force_download=True, proxies={"https": "http://localhost:7897"})
