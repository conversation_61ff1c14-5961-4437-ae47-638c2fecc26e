from ultralytics import YOLO
from PIL import Image
import supervision as sv
import numpy as np

IOU_THRESHOLD = 0.3
CONFIDENCE_THRESHOLD = 0.2

pretrained_path = r"G:\pycharm\NSFW_Detection\model_path\nsfw_mosaic\erax-anti-nsfw-yolo11m-v1.1.pt"

image_path_list = [r"E:\ComfyUI\output\ComfyUI_00285_.png", r"E:\ComfyUI\output\ComfyUI_00279_.png"]

model = YOLO(pretrained_path)
results = model(image_path_list,
                conf=CONFIDENCE_THRESHOLD,
                iou=IOU_THRESHOLD
                )

for result in results:
    annotated_image = result.orig_img.copy()
    h, w = annotated_image.shape[:2]
    anchor = h if h > w else w

    detections = sv.Detections.from_ultralytics(result)
    label_annotator = sv.LabelAnnotator(text_color=sv.Color.BLACK,
                                        text_position=sv.Position.CENTER,
                                        text_scale=anchor / 1700)

    pixelate_annotator = sv.PixelateAnnotator(pixel_size=anchor / 50)

    annotated_image = pixelate_annotator.annotate(
        scene=annotated_image.copy(),
        detections=detections
    )

    annotated_image = label_annotator.annotate(
        annotated_image,
        detections=detections
    )

    sv.plot_image(annotated_image, size=(10, 10))