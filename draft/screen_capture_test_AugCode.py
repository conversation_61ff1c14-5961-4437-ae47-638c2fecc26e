"""
Screen Capture Test - 解决MSS线程问题
测试不同的屏幕捕获方法
"""

import os
import sys
import time
import threading
import logging
from PIL import Image, ImageGrab
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Try to import MSS
try:
    import mss
    MSS_AVAILABLE = True
    print("✅ MSS library available")
except ImportError:
    MSS_AVAILABLE = False
    print("❌ MSS library not available")

class ThreadSafeScreenCapture:
    """线程安全的屏幕捕获类"""
    
    def __init__(self):
        self.use_mss = MSS_AVAILABLE
        self.use_pil_fallback = False
        
    def capture_with_pil(self) -> Image.Image:
        """使用PIL ImageGrab捕获屏幕"""
        try:
            img = ImageGrab.grab()
            logging.info(f"PIL capture successful: {img.size}")
            return img.convert("RGB")
        except Exception as e:
            logging.error(f"PIL capture failed: {e}")
            raise
    
    def capture_with_mss_simple(self) -> Image.Image:
        """使用MSS简单捕获（每次创建新实例）"""
        if not MSS_AVAILABLE:
            raise RuntimeError("MSS not available")
        
        try:
            # 每次都创建新的MSS实例，避免线程问题
            with mss.mss() as sct:
                monitor = sct.monitors[1]  # Primary monitor
                screenshot = sct.grab(monitor)
                img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
                logging.info(f"MSS capture successful: {img.size}")
                return img.convert("RGB")
        except Exception as e:
            logging.error(f"MSS capture failed: {e}")
            raise
    
    def capture_screen_safe(self) -> Image.Image:
        """安全的屏幕捕获方法"""
        # 如果已经标记为使用PIL fallback，直接使用PIL
        if self.use_pil_fallback:
            return self.capture_with_pil()
        
        # 尝试MSS
        if self.use_mss:
            try:
                return self.capture_with_mss_simple()
            except Exception as e:
                logging.warning(f"MSS failed: {e}, switching to PIL permanently")
                self.use_pil_fallback = True
                return self.capture_with_pil()
        else:
            return self.capture_with_pil()

def test_capture_methods():
    """测试不同的捕获方法"""
    print("\n🧪 Testing different screen capture methods...")
    
    capture = ThreadSafeScreenCapture()
    
    # Test 1: PIL method
    print("\n1. Testing PIL ImageGrab...")
    try:
        start_time = time.time()
        img_pil = capture.capture_with_pil()
        pil_time = time.time() - start_time
        print(f"✅ PIL capture: {img_pil.size}, time: {pil_time:.3f}s")
    except Exception as e:
        print(f"❌ PIL capture failed: {e}")
    
    # Test 2: MSS method
    if MSS_AVAILABLE:
        print("\n2. Testing MSS...")
        try:
            start_time = time.time()
            img_mss = capture.capture_with_mss_simple()
            mss_time = time.time() - start_time
            print(f"✅ MSS capture: {img_mss.size}, time: {mss_time:.3f}s")
        except Exception as e:
            print(f"❌ MSS capture failed: {e}")
    
    # Test 3: Safe method
    print("\n3. Testing safe capture method...")
    try:
        start_time = time.time()
        img_safe = capture.capture_screen_safe()
        safe_time = time.time() - start_time
        print(f"✅ Safe capture: {img_safe.size}, time: {safe_time:.3f}s")
    except Exception as e:
        print(f"❌ Safe capture failed: {e}")

def test_threading():
    """测试多线程环境下的屏幕捕获"""
    print("\n🧵 Testing screen capture in threading environment...")
    
    results = []
    errors = []
    
    def capture_worker(worker_id, num_captures=3):
        """工作线程函数"""
        capture = ThreadSafeScreenCapture()
        thread_results = []
        
        for i in range(num_captures):
            try:
                start_time = time.time()
                img = capture.capture_screen_safe()
                capture_time = time.time() - start_time
                
                result = {
                    'worker_id': worker_id,
                    'capture_id': i,
                    'size': img.size,
                    'time': capture_time,
                    'method': 'PIL' if capture.use_pil_fallback else 'MSS'
                }
                thread_results.append(result)
                print(f"Worker {worker_id}, Capture {i}: {img.size}, {capture_time:.3f}s, {result['method']}")
                
                time.sleep(0.5)  # Small delay between captures
                
            except Exception as e:
                error = f"Worker {worker_id}, Capture {i}: {e}"
                errors.append(error)
                print(f"❌ {error}")
        
        results.extend(thread_results)
    
    # Create and start threads
    threads = []
    num_workers = 3
    
    for worker_id in range(num_workers):
        thread = threading.Thread(target=capture_worker, args=(worker_id,))
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    # Print results
    print(f"\n📊 Threading test results:")
    print(f"Total successful captures: {len(results)}")
    print(f"Total errors: {len(errors)}")
    
    if results:
        avg_time = sum(r['time'] for r in results) / len(results)
        print(f"Average capture time: {avg_time:.3f}s")
        
        methods_used = set(r['method'] for r in results)
        print(f"Methods used: {', '.join(methods_used)}")
    
    if errors:
        print("Errors encountered:")
        for error in errors:
            print(f"  - {error}")

def test_continuous_capture():
    """测试连续捕获"""
    print("\n⏰ Testing continuous screen capture...")
    
    capture = ThreadSafeScreenCapture()
    num_captures = 10
    
    print(f"Performing {num_captures} continuous captures...")
    
    times = []
    for i in range(num_captures):
        try:
            start_time = time.time()
            img = capture.capture_screen_safe()
            capture_time = time.time() - start_time
            times.append(capture_time)
            
            method = 'PIL' if capture.use_pil_fallback else 'MSS'
            print(f"Capture {i+1}: {img.size}, {capture_time:.3f}s, {method}")
            
            time.sleep(0.2)  # Small delay
            
        except Exception as e:
            print(f"❌ Capture {i+1} failed: {e}")
    
    if times:
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        fps = 1.0 / avg_time if avg_time > 0 else 0
        
        print(f"\n📈 Continuous capture statistics:")
        print(f"Average time: {avg_time:.3f}s")
        print(f"Min time: {min_time:.3f}s")
        print(f"Max time: {max_time:.3f}s")
        print(f"Estimated FPS: {fps:.1f}")

def main():
    """主函数"""
    print("🖥️  Screen Capture Test - MSS Threading Fix")
    print("="*60)
    
    # Test individual methods
    test_capture_methods()
    
    # Test threading
    test_threading()
    
    # Test continuous capture
    test_continuous_capture()
    
    print("\n✅ All tests completed!")

if __name__ == "__main__":
    main()
