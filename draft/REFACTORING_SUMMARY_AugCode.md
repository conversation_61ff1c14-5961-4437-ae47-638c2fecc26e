# Real-Time NSFW Screen Monitor - Destructive Refactoring Summary

## Overview
This document summarizes the destructive refactoring of `draft/test_detection.py` into a comprehensive real-time NSFW screen monitoring system.

## 🎯 Requirements Fulfilled

### ✅ 1. Real-time Screen Monitoring
- **Implementation**: `ScreenCapture` class with MSS and PIL ImageGrab fallback
- **Features**: 
  - Continuous screen capture at configurable intervals
  - Automatic image resizing for performance optimization
  - Primary monitor detection and capture

### ✅ 2. NSFW Detection
- **Multi-model Support**: 
  - **ViT Binary Classifier** (`model_path/nsfw_detection`): normal/nsfw classification
  - **YOLO11 Object Detection** (`model_path/nsfw_mosaic`): Detects specific NSFW objects
  - **Multi-class Classifier** (`model_path/nsfw_classifier`): 5-class classification
- **Confidence Thresholding**: Configurable NSFW detection threshold
- **GPU Acceleration**: CUDA support for faster inference

### ✅ 3. Automatic NSFW Image Saving
- **Save Location**: `/draft` directory (configurable)
- **File Naming**: `nsfw_YYYYMMDD_HHMMSS_mmm.png` with millisecond precision
- **Metadata**: JSON files with detection results, timestamps, and model details
- **Conditional Saving**: Only saves when NSFW content is detected above threshold

### ✅ 4. Resource Consumption Monitoring
- **CPU Usage**: Real-time CPU percentage monitoring
- **Memory Usage**: Process-specific and system memory tracking
- **GPU Memory**: CUDA memory allocation tracking
- **Logging**: Periodic resource usage logging with configurable intervals

### ✅ 5. Performance Metrics & FPS Calculation
- **Inference Time**: Per-model inference time measurement
- **FPS Calculation**: Real-time FPS based on capture rate and processing time
- **Average Metrics**: Rolling averages for performance analysis
- **Statistics**: Comprehensive final statistics report

## 🏗️ Architecture Overview

### Core Classes

#### `MonitorConfig`
- Configuration dataclass for all monitoring parameters
- Includes model paths, thresholds, performance settings, and logging options

#### `DetectionResult`
- Dataclass storing individual detection results
- Contains timestamp, confidence, model used, inference time, and details

#### `ResourceMetrics`
- Dataclass for system resource measurements
- Tracks CPU, memory, and GPU usage with timestamps

#### `ScreenCapture`
- Handles screen capture operations
- Supports MSS (faster) and PIL ImageGrab (fallback)
- Automatic image resizing for performance

#### `NSFWModelManager`
- Manages multiple NSFW detection models
- Lazy loading and GPU acceleration support
- Unified prediction interface for all model types

#### `ResourceMonitor`
- System resource monitoring with psutil
- Historical metrics tracking with rolling averages
- Process-specific and system-wide metrics

#### `RealTimeNSFWMonitor`
- Main orchestrator class
- Threading-based monitoring loop
- Statistics tracking and reporting
- Automatic file saving and logging

## 🚀 Key Features

### Multi-Model Detection
```python
# Supports three different model types:
1. ViT Binary: normal/nsfw classification
2. YOLO11: Object detection (anus, make_love, nipple, penis, vagina)
3. Multi-class: 5-class classification (drawings, hentai, neutral, porn, sexy)
```

### Performance Optimization
- **GPU Acceleration**: Automatic CUDA detection and usage
- **Efficient Screen Capture**: MSS library for faster screenshots
- **Image Resizing**: Automatic downscaling for faster processing
- **Threading**: Non-blocking monitoring loop

### Resource Monitoring
```python
# Tracks multiple metrics:
- CPU usage percentage
- Memory usage (process and system)
- GPU memory allocation
- Inference time per model
- Overall FPS calculation
```

### Comprehensive Logging
- **File Logging**: Optional log file output
- **Console Output**: Real-time status updates
- **Metadata Saving**: JSON files with detection details
- **Statistics**: Final performance report

## 📊 Performance Metrics

### Real-time Monitoring
- **Status Updates**: Every 10 captures
- **Resource Logging**: Configurable interval (default: 10s)
- **FPS Calculation**: Based on actual capture and processing times
- **Memory Tracking**: Process-specific memory usage

### Final Statistics
- Total runtime and captures
- NSFW detection rate and count
- Average FPS and inference times
- Resource usage averages
- GPU memory utilization (if available)

## 🔧 Configuration Options

### Monitoring Settings
- `capture_interval`: Time between screen captures (default: 1.0s)
- `nsfw_threshold`: Confidence threshold for NSFW detection (default: 0.7)
- `save_nsfw_images`: Enable/disable automatic saving (default: True)
- `save_directory`: Directory for saved images (default: "draft")

### Model Settings
- `use_vit_model`: Enable ViT binary classifier (default: True)
- `use_yolo_model`: Enable YOLO object detection (default: True)
- `use_multiclass_model`: Enable multi-class classifier (default: False)
- Model paths for each type

### Performance Settings
- `max_image_size`: Maximum image dimensions for processing
- `enable_gpu`: GPU acceleration toggle
- `resource_log_interval`: Resource monitoring frequency

## 🎮 Usage Examples

### Basic Usage
```python
# Create default configuration
config = create_default_config()

# Initialize and start monitoring
monitor = RealTimeNSFWMonitor(config)
monitor.start_monitoring()

# Monitor runs until Ctrl+C
```

### Single Image Testing
```python
# Test detection on a single image
results = test_single_image_detection("path/to/image.jpg")
```

### Custom Configuration
```python
# Create custom configuration
config = MonitorConfig(
    capture_interval=0.5,  # Faster capture
    nsfw_threshold=0.8,    # Higher threshold
    use_yolo_model=False,  # Disable YOLO
    log_level="DEBUG"      # Verbose logging
)
```

## 📁 File Structure

### Generated Files
- `nsfw_YYYYMMDD_HHMMSS_mmm.png`: NSFW screenshots
- `nsfw_YYYYMMDD_HHMMSS_mmm_metadata.json`: Detection metadata
- `nsfw_monitor.log`: Application log file (optional)

### Metadata Format
```json
{
  "timestamp": "20241231_235959_123",
  "total_captures": 1234,
  "detection_results": [
    {
      "model": "vit",
      "is_nsfw": true,
      "confidence": 0.95,
      "inference_time": 0.123,
      "details": {...}
    }
  ]
}
```

## 🔍 System Requirements

### Required Libraries
- `torch`: PyTorch for model inference
- `PIL`: Image processing
- `numpy`: Numerical operations

### Optional Libraries (Enhanced Features)
- `transformers`: ViT and multi-class models
- `ultralytics`: YOLO11 models
- `mss`: Fast screen capture
- `psutil`: Resource monitoring
- `onnxruntime`: ONNX model support

### Hardware Recommendations
- **GPU**: NVIDIA GPU with CUDA support for faster inference
- **RAM**: 8GB+ recommended for model loading
- **Storage**: SSD recommended for fast image saving

## 🚨 Important Notes

### Privacy and Legal Considerations
- This tool captures and analyzes screen content
- Ensure compliance with local privacy laws
- Use responsibly and with appropriate consent

### Performance Considerations
- GPU acceleration significantly improves performance
- Screen capture frequency affects system load
- Model loading requires initial memory allocation

### Error Handling
- Graceful degradation when models are missing
- Automatic fallbacks for optional dependencies
- Comprehensive error logging and recovery

## 🎉 Conclusion

The destructive refactoring successfully transformed a simple test script into a comprehensive real-time NSFW monitoring system with:

- ✅ Real-time screen monitoring
- ✅ Multi-model NSFW detection
- ✅ Automatic NSFW image saving
- ✅ Resource consumption monitoring
- ✅ Performance metrics and FPS calculation
- ✅ Comprehensive logging and statistics
- ✅ Configurable parameters and thresholds
- ✅ GPU acceleration support
- ✅ Error handling and graceful degradation

The system is production-ready with proper error handling, logging, and performance optimization.
