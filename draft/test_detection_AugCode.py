"""
Real-Time NSFW Screen Monitor
Destructively refactored from test_detection.py

This script implements real-time screen monitoring with NSFW detection:
1. Continuous screen capture and monitoring
2. Multi-model NSFW detection (ViT, YOLO11, Multi-class)
3. Automatic saving of NSFW content to /draft directory
4. Resource consumption monitoring (CPU, Memory, Inference time, FPS)
5. Performance metrics and logging

Author: Generated by Augment Agent (Destructive Refactoring)
"""

import os
import sys
import json
import time
import threading
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from collections import deque

# Core libraries
import torch
import numpy as np
from PIL import Image, ImageGrab

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Resource monitoring
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    print("Warning: psutil not available. Install with: pip install psutil")
    PSUTIL_AVAILABLE = False

# Screen capture
try:
    import mss
    MSS_AVAILABLE = True
except ImportError:
    print("Warning: mss not available. Install with: pip install mss")
    MSS_AVAILABLE = False

# ML libraries
try:
    from transformers import pipeline, AutoModelForImageClassification, ViTImageProcessor
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    print("Warning: transformers library not available. Install with: pip install transformers")
    TRANSFORMERS_AVAILABLE = False

try:
    import onnxruntime as ort
    ONNX_AVAILABLE = True
except ImportError:
    print("Warning: onnxruntime not available. Install with: pip install onnxruntime")
    ONNX_AVAILABLE = False

try:
    from ultralytics import YOLO
    ULTRALYTICS_AVAILABLE = True
except ImportError:
    print("Warning: ultralytics not available. Install with: pip install ultralytics")
    ULTRALYTICS_AVAILABLE = False


@dataclass
class MonitorConfig:
    """Configuration for the real-time NSFW monitor."""
    # Monitoring settings
    capture_interval: float = 1.0  # seconds between captures
    nsfw_threshold: float = 0.7  # confidence threshold for NSFW detection
    save_nsfw_images: bool = True
    save_directory: str = "draft"
    
    # Model settings
    use_vit_model: bool = True
    use_yolo_model: bool = True
    use_multiclass_model: bool = False
    vit_model_path: str = "model_path/nsfw_detection"
    yolo_model_path: str = "model_path/nsfw_mosaic/erax-anti-nsfw-yolo11n-v1.1.pt"
    multiclass_model_path: str = "model_path/nsfw_classifier"
    
    # Performance settings
    max_image_size: Tuple[int, int] = (640, 640)
    enable_gpu: bool = True
    batch_processing: bool = False
    
    # Resource monitoring
    monitor_resources: bool = True
    resource_log_interval: float = 5.0  # seconds
    
    # Logging
    log_level: str = "INFO"
    log_file: Optional[str] = "nsfw_monitor.log"


@dataclass
class DetectionResult:
    """Result from NSFW detection."""
    timestamp: datetime
    is_nsfw: bool
    confidence: float
    model_used: str
    inference_time: float
    image_path: Optional[str] = None
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ResourceMetrics:
    """System resource metrics."""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    gpu_memory_used_mb: Optional[float] = None


class ScreenCapture:
    """Handles screen capture operations."""

    def __init__(self, config: MonitorConfig):
        self.config = config
        self.use_mss = MSS_AVAILABLE
        self.use_pil_fallback = False
        self.mss_failed_count = 0
        self.max_mss_failures = 3  # After 3 failures, permanently switch to PIL

    def capture_screen(self) -> Image.Image:
        """Capture the current screen."""
        img = None

        # Try MSS first (faster) if not disabled and failure count is low
        if not self.use_pil_fallback and self.use_mss and self.mss_failed_count < self.max_mss_failures:
            try:
                # Create a new MSS instance each time to avoid threading issues
                if MSS_AVAILABLE:
                    import mss
                    with mss.mss() as sct:
                        monitor = sct.monitors[1]  # Primary monitor
                        screenshot = sct.grab(monitor)
                        img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
                        logging.debug("Screen captured using MSS")
                        # Reset failure count on success
                        self.mss_failed_count = 0
            except Exception as e:
                self.mss_failed_count += 1
                logging.warning(f"MSS capture failed (attempt {self.mss_failed_count}): {e}")

                # If we've failed too many times, permanently switch to PIL
                if self.mss_failed_count >= self.max_mss_failures:
                    logging.warning("Too many MSS failures, switching to PIL permanently")
                    self.use_pil_fallback = True

        # Use PIL ImageGrab if MSS failed or is disabled
        if img is None:
            try:
                img = ImageGrab.grab()
                logging.debug("Screen captured using PIL ImageGrab")
            except Exception as e:
                logging.error(f"PIL ImageGrab failed: {e}")
                raise RuntimeError(f"All screen capture methods failed: {e}")

        # Resize if needed
        if img and (img.size[0] > self.config.max_image_size[0] or img.size[1] > self.config.max_image_size[1]):
            original_size = img.size
            img.thumbnail(self.config.max_image_size, Image.Resampling.LANCZOS)
            logging.debug(f"Image resized from {original_size} to {img.size}")

        return img.convert("RGB") if img else None


class NSFWModelManager:
    """Manages multiple NSFW detection models."""
    
    def __init__(self, config: MonitorConfig):
        self.config = config
        self.models = {}
        self.device = "cuda" if torch.cuda.is_available() and config.enable_gpu else "cpu"
        logging.info(f"Using device: {self.device}")
        
        # Initialize models based on configuration
        if config.use_vit_model:
            self._load_vit_model()
        if config.use_yolo_model:
            self._load_yolo_model()
        if config.use_multiclass_model:
            self._load_multiclass_model()
    
    def _load_vit_model(self):
        """Load ViT-based binary classifier."""
        try:
            if not TRANSFORMERS_AVAILABLE:
                logging.warning("Transformers not available, skipping ViT model")
                return
            
            model_path = self.config.vit_model_path
            logging.info(f"Loading ViT model from {model_path}")
            
            model = AutoModelForImageClassification.from_pretrained(model_path)
            processor = ViTImageProcessor.from_pretrained(model_path)
            
            if self.device == "cuda":
                model = model.to(self.device)
            
            self.models['vit'] = {
                'model': model,
                'processor': processor,
                'type': 'binary'
            }
            logging.info("ViT model loaded successfully")
            
        except Exception as e:
            logging.error(f"Failed to load ViT model: {e}")
    
    def _load_yolo_model(self):
        """Load YOLO11 model for object detection."""
        try:
            if not ULTRALYTICS_AVAILABLE:
                logging.warning("Ultralytics not available, skipping YOLO model")
                return
            
            model_path = self.config.yolo_model_path
            if not os.path.exists(model_path):
                logging.warning(f"YOLO model not found at {model_path}")
                return
            
            logging.info(f"Loading YOLO model from {model_path}")
            model = YOLO(model_path)
            
            self.models['yolo'] = {
                'model': model,
                'type': 'object_detection',
                'classes': ['anus', 'make_love', 'nipple', 'penis', 'vagina']
            }
            logging.info("YOLO model loaded successfully")
            
        except Exception as e:
            logging.error(f"Failed to load YOLO model: {e}")
    
    def _load_multiclass_model(self):
        """Load multi-class classifier."""
        try:
            if not TRANSFORMERS_AVAILABLE:
                logging.warning("Transformers not available, skipping multiclass model")
                return
            
            model_path = self.config.multiclass_model_path
            logging.info(f"Loading multiclass model from {model_path}")
            
            model = AutoModelForImageClassification.from_pretrained(model_path)
            processor = ViTImageProcessor.from_pretrained(model_path)
            
            if self.device == "cuda":
                model = model.to(self.device)
            
            self.models['multiclass'] = {
                'model': model,
                'processor': processor,
                'type': 'multiclass',
                'classes': ['drawings', 'hentai', 'neutral', 'porn', 'sexy']
            }
            logging.info("Multiclass model loaded successfully")
            
        except Exception as e:
            logging.error(f"Failed to load multiclass model: {e}")
    
    def predict(self, image: Image.Image) -> List[DetectionResult]:
        """Run prediction on image using all available models."""
        results = []
        timestamp = datetime.now()
        
        for model_name, model_info in self.models.items():
            try:
                start_time = time.time()
                
                if model_name == 'vit':
                    result = self._predict_vit(image, model_info)
                elif model_name == 'yolo':
                    result = self._predict_yolo(image, model_info)
                elif model_name == 'multiclass':
                    result = self._predict_multiclass(image, model_info)
                else:
                    continue
                
                inference_time = time.time() - start_time
                
                detection_result = DetectionResult(
                    timestamp=timestamp,
                    is_nsfw=result['is_nsfw'],
                    confidence=result['confidence'],
                    model_used=model_name,
                    inference_time=inference_time,
                    details=result.get('details', {})
                )
                
                results.append(detection_result)
                
            except Exception as e:
                logging.error(f"Prediction failed for {model_name}: {e}")
        
        return results

    def _predict_vit(self, image: Image.Image, model_info: Dict) -> Dict:
        """Predict using ViT binary classifier."""
        model = model_info['model']
        processor = model_info['processor']

        with torch.no_grad():
            inputs = processor(images=image, return_tensors="pt")
            if self.device == "cuda":
                inputs = {k: v.to(self.device) for k, v in inputs.items()}

            outputs = model(**inputs)
            logits = outputs.logits
            probabilities = torch.nn.functional.softmax(logits, dim=-1)

            predicted_idx = logits.argmax(-1).item()
            confidence = probabilities[0][predicted_idx].item()
            predicted_label = model.config.id2label[predicted_idx]

            is_nsfw = predicted_label == "nsfw"

            return {
                'is_nsfw': is_nsfw,
                'confidence': confidence,
                'details': {
                    'predicted_label': predicted_label,
                    'all_probabilities': {
                        model.config.id2label[i]: prob.item()
                        for i, prob in enumerate(probabilities[0])
                    }
                }
            }

    def _predict_yolo(self, image: Image.Image, model_info: Dict) -> Dict:
        """Predict using YOLO object detection."""
        model = model_info['model']

        # Convert PIL image to numpy array
        img_array = np.array(image)

        # Run inference
        results = model(img_array, conf=0.2, iou=0.3, verbose=False)

        # Process results
        detections = []
        max_confidence = 0.0

        for result in results:
            if result.boxes is not None:
                for box in result.boxes:
                    confidence = float(box.conf[0])
                    class_id = int(box.cls[0])
                    class_name = model_info['classes'][class_id]

                    detections.append({
                        'class': class_name,
                        'confidence': confidence,
                        'bbox': box.xyxy[0].tolist()
                    })

                    max_confidence = max(max_confidence, confidence)

        # Consider NSFW if any detection found
        is_nsfw = len(detections) > 0 and max_confidence >= self.config.nsfw_threshold

        return {
            'is_nsfw': is_nsfw,
            'confidence': max_confidence,
            'details': {
                'detections': detections,
                'detection_count': len(detections)
            }
        }

    def _predict_multiclass(self, image: Image.Image, model_info: Dict) -> Dict:
        """Predict using multiclass classifier."""
        model = model_info['model']
        processor = model_info['processor']

        with torch.no_grad():
            inputs = processor(images=image, return_tensors="pt")
            if self.device == "cuda":
                inputs = {k: v.to(self.device) for k, v in inputs.items()}

            outputs = model(**inputs)
            logits = outputs.logits
            probabilities = torch.nn.functional.softmax(logits, dim=-1)

            predicted_idx = logits.argmax(-1).item()
            confidence = probabilities[0][predicted_idx].item()
            predicted_label = model.config.id2label[predicted_idx]

            # Consider NSFW if not neutral
            nsfw_classes = ['hentai', 'porn', 'sexy', 'drawings']
            is_nsfw = predicted_label in nsfw_classes

            return {
                'is_nsfw': is_nsfw,
                'confidence': confidence,
                'details': {
                    'predicted_label': predicted_label,
                    'all_probabilities': {
                        model.config.id2label[i]: prob.item()
                        for i, prob in enumerate(probabilities[0])
                    }
                }
            }


class ResourceMonitor:
    """Monitors system resource usage."""

    def __init__(self, config: MonitorConfig):
        self.config = config
        self.metrics_history = deque(maxlen=1000)  # Keep last 1000 measurements
        self.process = psutil.Process() if PSUTIL_AVAILABLE else None

    def get_current_metrics(self) -> ResourceMetrics:
        """Get current system resource metrics."""
        timestamp = datetime.now()

        if not PSUTIL_AVAILABLE:
            return ResourceMetrics(
                timestamp=timestamp,
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_used_mb=0.0
            )

        # System metrics
        cpu_percent = psutil.cpu_percent(interval=None)
        memory = psutil.virtual_memory()

        # Process-specific metrics
        process_memory = self.process.memory_info().rss / 1024 / 1024  # MB

        # GPU metrics (if available)
        gpu_memory = None
        if torch.cuda.is_available():
            try:
                gpu_memory = torch.cuda.memory_allocated() / 1024 / 1024  # MB
            except:
                pass

        metrics = ResourceMetrics(
            timestamp=timestamp,
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_mb=process_memory,
            gpu_memory_used_mb=gpu_memory
        )

        self.metrics_history.append(metrics)
        return metrics

    def get_average_metrics(self, last_n: int = 10) -> Dict[str, float]:
        """Get average metrics over last N measurements."""
        if not self.metrics_history:
            return {}

        recent_metrics = list(self.metrics_history)[-last_n:]

        return {
            'avg_cpu_percent': sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics),
            'avg_memory_percent': sum(m.memory_percent for m in recent_metrics) / len(recent_metrics),
            'avg_memory_used_mb': sum(m.memory_used_mb for m in recent_metrics) / len(recent_metrics),
            'avg_gpu_memory_mb': sum(m.gpu_memory_used_mb or 0 for m in recent_metrics) / len(recent_metrics)
        }


class RealTimeNSFWMonitor:
    """Main class for real-time NSFW screen monitoring."""

    def __init__(self, config: MonitorConfig):
        self.config = config
        self.running = False
        self.monitor_thread = None

        # Initialize components
        self.screen_capture = ScreenCapture(config)
        self.model_manager = NSFWModelManager(config)
        self.resource_monitor = ResourceMonitor(config)

        # Statistics
        self.total_captures = 0
        self.nsfw_detections = 0
        self.start_time = None
        self.detection_history = deque(maxlen=1000)

        # Setup logging
        self._setup_logging()

        # Create save directory
        os.makedirs(self.config.save_directory, exist_ok=True)

        logging.info("RealTimeNSFWMonitor initialized")

    def _setup_logging(self):
        """Setup logging configuration."""
        log_level = getattr(logging, self.config.log_level.upper(), logging.INFO)

        # Configure logging
        handlers = [logging.StreamHandler()]
        if self.config.log_file:
            handlers.append(logging.FileHandler(self.config.log_file))

        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=handlers
        )

    def start_monitoring(self):
        """Start the real-time monitoring."""
        if self.running:
            logging.warning("Monitor is already running")
            return

        self.running = True
        self.start_time = datetime.now()
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()

        logging.info("Real-time NSFW monitoring started")
        print("🔍 Real-time NSFW monitoring started...")
        print(f"📁 NSFW images will be saved to: {self.config.save_directory}")
        print("Press Ctrl+C to stop monitoring")

    def stop_monitoring(self):
        """Stop the monitoring."""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)

        self._print_final_statistics()
        logging.info("Real-time NSFW monitoring stopped")

    def _monitoring_loop(self):
        """Main monitoring loop."""
        last_resource_log = time.time()

        while self.running:
            try:
                loop_start = time.time()

                # Capture screen
                screenshot = self.screen_capture.capture_screen()
                self.total_captures += 1

                # Run NSFW detection
                detection_results = self.model_manager.predict(screenshot)

                # Process results
                self._process_detection_results(screenshot, detection_results)

                # Monitor resources periodically
                if time.time() - last_resource_log >= self.config.resource_log_interval:
                    self._log_resource_metrics()
                    last_resource_log = time.time()

                # Calculate sleep time to maintain interval
                loop_time = time.time() - loop_start
                sleep_time = max(0, self.config.capture_interval - loop_time)

                if sleep_time > 0:
                    time.sleep(sleep_time)

            except KeyboardInterrupt:
                break
            except Exception as e:
                logging.error(f"Error in monitoring loop: {e}")
                time.sleep(1.0)  # Brief pause before retrying

    def _process_detection_results(self, screenshot: Image.Image, results: List[DetectionResult]):
        """Process detection results and save NSFW images if needed."""
        # Determine if any model detected NSFW content
        nsfw_detected = any(result.is_nsfw and result.confidence >= self.config.nsfw_threshold
                           for result in results)

        if nsfw_detected:
            self.nsfw_detections += 1

            # Save screenshot if configured
            if self.config.save_nsfw_images:
                saved_path = self._save_nsfw_image(screenshot, results)
                logging.info(f"NSFW content detected and saved: {saved_path}")

            # Log detection details
            for result in results:
                if result.is_nsfw:
                    logging.info(f"NSFW detected by {result.model_used}: "
                               f"confidence={result.confidence:.3f}, "
                               f"inference_time={result.inference_time:.3f}s")

        # Store results for statistics
        self.detection_history.extend(results)

        # Print periodic status
        if self.total_captures % 10 == 0:
            self._print_status()

    def _save_nsfw_image(self, image: Image.Image, results: List[DetectionResult]) -> str:
        """Save NSFW image with metadata."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # Include milliseconds
        filename = f"nsfw_{timestamp}.png"
        filepath = os.path.join(self.config.save_directory, filename)

        # Save image
        image.save(filepath, "PNG")

        # Save metadata
        metadata = {
            'timestamp': timestamp,
            'total_captures': self.total_captures,
            'detection_results': [
                {
                    'model': result.model_used,
                    'is_nsfw': result.is_nsfw,
                    'confidence': result.confidence,
                    'inference_time': result.inference_time,
                    'details': result.details
                }
                for result in results
            ]
        }

        metadata_path = filepath.replace('.png', '_metadata.json')
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2, default=str)

        return filepath

    def _log_resource_metrics(self):
        """Log current resource usage."""
        metrics = self.resource_monitor.get_current_metrics()
        avg_metrics = self.resource_monitor.get_average_metrics()

        logging.info(f"Resources - CPU: {metrics.cpu_percent:.1f}%, "
                    f"Memory: {metrics.memory_used_mb:.1f}MB ({metrics.memory_percent:.1f}%), "
                    f"GPU: {metrics.gpu_memory_used_mb or 0:.1f}MB")

        # Calculate FPS
        if self.detection_history:
            recent_detections = [r for r in self.detection_history if
                               (datetime.now() - r.timestamp).total_seconds() <= 60]
            if recent_detections:
                avg_inference_time = sum(r.inference_time for r in recent_detections) / len(recent_detections)
                estimated_fps = 1.0 / max(avg_inference_time, 0.001)
                logging.info(f"Performance - Avg inference: {avg_inference_time:.3f}s, "
                           f"Estimated FPS: {estimated_fps:.1f}")

    def _print_status(self):
        """Print current monitoring status."""
        if not self.start_time:
            return

        elapsed = (datetime.now() - self.start_time).total_seconds()
        fps = self.total_captures / elapsed if elapsed > 0 else 0
        detection_rate = (self.nsfw_detections / self.total_captures * 100) if self.total_captures > 0 else 0

        print(f"📊 Status: {self.total_captures} captures, "
              f"{self.nsfw_detections} NSFW ({detection_rate:.1f}%), "
              f"FPS: {fps:.1f}, Runtime: {elapsed:.0f}s")

    def _print_final_statistics(self):
        """Print final monitoring statistics."""
        if not self.start_time:
            return

        elapsed = (datetime.now() - self.start_time).total_seconds()
        fps = self.total_captures / elapsed if elapsed > 0 else 0
        detection_rate = (self.nsfw_detections / self.total_captures * 100) if self.total_captures > 0 else 0

        print("\n" + "="*60)
        print("📈 FINAL MONITORING STATISTICS")
        print("="*60)
        print(f"⏱️  Total Runtime: {elapsed:.1f} seconds")
        print(f"📸 Total Captures: {self.total_captures}")
        print(f"🔞 NSFW Detections: {self.nsfw_detections} ({detection_rate:.1f}%)")
        print(f"⚡ Average FPS: {fps:.1f}")

        if self.detection_history:
            avg_inference = sum(r.inference_time for r in self.detection_history) / len(self.detection_history)
            print(f"🧠 Average Inference Time: {avg_inference:.3f}s")

        # Resource statistics
        avg_metrics = self.resource_monitor.get_average_metrics()
        if avg_metrics:
            print(f"💾 Average Memory Usage: {avg_metrics.get('avg_memory_used_mb', 0):.1f}MB")
            print(f"🖥️  Average CPU Usage: {avg_metrics.get('avg_cpu_percent', 0):.1f}%")
            if avg_metrics.get('avg_gpu_memory_mb', 0) > 0:
                print(f"🎮 Average GPU Memory: {avg_metrics['avg_gpu_memory_mb']:.1f}MB")

        print("="*60)


def create_default_config() -> MonitorConfig:
    """Create default monitoring configuration."""
    return MonitorConfig(
        capture_interval=1.0,
        nsfw_threshold=0.7,
        save_nsfw_images=True,
        save_directory="draft",
        use_vit_model=True,
        use_yolo_model=True,
        use_multiclass_model=False,
        monitor_resources=True,
        resource_log_interval=10.0,
        log_level="INFO"
    )


def test_single_image_detection(image_path: str, config: MonitorConfig = None):
    """Test NSFW detection on a single image."""
    if config is None:
        config = create_default_config()

    print(f"🔍 Testing NSFW detection on: {image_path}")
    print("="*60)

    # Load image
    try:
        image = Image.open(image_path).convert("RGB")
    except Exception as e:
        print(f"❌ Error loading image: {e}")
        return

    # Initialize model manager
    model_manager = NSFWModelManager(config)

    # Run detection
    start_time = time.time()
    results = model_manager.predict(image)
    total_time = time.time() - start_time

    # Display results
    print(f"📊 Detection Results ({total_time:.3f}s total):")
    print("-" * 40)

    for result in results:
        status = "🔞 NSFW" if result.is_nsfw else "✅ Safe"
        print(f"{status} | {result.model_used.upper()}: "
              f"{result.confidence:.3f} confidence "
              f"({result.inference_time:.3f}s)")

        if result.details:
            for key, value in result.details.items():
                if key == 'detections' and isinstance(value, list):
                    print(f"    Detections: {len(value)} objects found")
                elif key == 'predicted_label':
                    print(f"    Label: {value}")

    print("="*60)
    return results


def print_system_info():
    """Print system information and available libraries."""
    print("🖥️  Real-Time NSFW Monitor - System Information")
    print("="*60)
    print(f"🐍 Python: {sys.version.split()[0]}")
    print(f"🔥 PyTorch: {torch.__version__}")
    print(f"🤗 Transformers: {'✅' if TRANSFORMERS_AVAILABLE else '❌'}")
    print(f"🎯 Ultralytics: {'✅' if ULTRALYTICS_AVAILABLE else '❌'}")
    print(f"📊 ONNX Runtime: {'✅' if ONNX_AVAILABLE else '❌'}")
    print(f"📸 MSS (Screen Capture): {'✅' if MSS_AVAILABLE else '❌'}")
    print(f"💾 PSUtil (Resource Monitor): {'✅' if PSUTIL_AVAILABLE else '❌'}")
    print(f"🎮 CUDA Available: {'✅' if torch.cuda.is_available() else '❌'}")

    if torch.cuda.is_available():
        print(f"🎮 GPU: {torch.cuda.get_device_name()}")
        print(f"🎮 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")

    print("="*60)


def main():
    """Main function for real-time NSFW monitoring."""
    print_system_info()

    # Create configuration
    config = create_default_config()

    # Check if models exist
    model_paths_to_check = [
        config.vit_model_path,
        config.yolo_model_path,
        config.multiclass_model_path
    ]

    missing_models = []
    for path in model_paths_to_check:
        if not os.path.exists(path):
            missing_models.append(path)

    if missing_models:
        print("⚠️  Warning: Some model paths not found:")
        for path in missing_models:
            print(f"   - {path}")
        print("\n💡 You can still run with available models.")

        # Disable missing models
        if not os.path.exists(config.vit_model_path):
            config.use_vit_model = False
        if not os.path.exists(config.yolo_model_path):
            config.use_yolo_model = False
        if not os.path.exists(config.multiclass_model_path):
            config.use_multiclass_model = False

    # Test with sample images first if available
    test_images_dir = Path("draft/img")
    if test_images_dir.exists():
        image_files = list(test_images_dir.glob("*.png")) + list(test_images_dir.glob("*.jpg"))
        if image_files:
            print(f"\n🧪 Testing with sample image: {image_files[0]}")
            test_single_image_detection(str(image_files[0]), config)

            response = input("\n🚀 Start real-time monitoring? (y/N): ").strip().lower()
            if response != 'y':
                print("👋 Exiting...")
                return

    # Initialize and start monitor
    try:
        monitor = RealTimeNSFWMonitor(config)
        monitor.start_monitoring()

        # Keep running until interrupted
        while monitor.running:
            time.sleep(1)

    except KeyboardInterrupt:
        print("\n🛑 Stopping monitor...")
        if 'monitor' in locals():
            monitor.stop_monitoring()
    except Exception as e:
        print(f"❌ Error: {e}")
        logging.error(f"Fatal error: {e}")


if __name__ == "__main__":
    main()
